{"name": "forachat", "version": "1.0.0", "description": "Group text for professional interpersonal skills enrichment", "homepage": "https://github.com/askfora/forachat#readme", "bugs": {"url": "https://github.com/askfora/forachat/issues"}, "repository": {"type": "git", "url": "git+https://github.com/askfora/forachat.git"}, "license": "ISC", "author": "<EMAIL>", "type": "commonjs", "main": "dist/index.js", "scripts": {"test": "test/index.js", "start": "npm run build:backend && node dist/index.js", "build": "npm run build:backend && npm run build:frontend", "build:backend": "tsc", "build:frontend": "vite build", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "npm run build:backend && node dist/index.js", "dev:frontend": "vite", "migrate": "npx knex migrate:latest", "repl": "npm run build:backend && node dist/repl.js"}, "dependencies": {"@dbos-inc/dbos-cloud": "^2.10.24", "@dbos-inc/dbos-sdk": "^2.10.24", "@google/generative-ai": "^0.24.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/uuid": "^10.0.0", "dotenv": "^17.0.1", "express": "^5.1.0", "knex": "^3.1.0", "pg": "^8.12.0", "react": "^19.1.0", "react-dom": "^19.1.0", "uuid": "^11.1.0", "ws": "^8.18.3"}, "devDependencies": {"@dbos-inc/dbos-sdk": "^2.10.24", "@types/express": "^5.0.3", "@types/koa__router": "^12.0.4", "@types/node": "^24.0.10", "@types/pg": "^8.15.4", "@types/ws": "^8.18.1", "@vitejs/plugin-react": "^4.6.0", "concurrently": "^9.2.0", "knex": "^3.1.0", "typescript": "^5.8.3", "vite": "^6.3.5"}}