import React from 'react'
import { useChat } from '../hooks/useChat'

const TypingIndicator: React.FC = () => {
  const { isTyping } = useChat()

  return (
    <div className={`typing-indicator ${isTyping ? 'show' : ''}`}>
      <div className="typing-dots">
        <div className="dot"></div>
        <div className="dot"></div>
        <div className="dot"></div>
      </div>
    </div>
  )
}

export default TypingIndicator
