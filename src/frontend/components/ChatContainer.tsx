import React, { useEffect, useRef } from 'react'
import { useChat } from '../hooks/useChat'
import Message from './Message'
import Skills from './Skills'

const ChatContainer: React.FC = () => {
  const { messages, skills } = useChat()
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight
    }
  }, [messages])

  return (
    <div
      className="chat-container"
      ref={containerRef}
    >
      <div className="message system">
        <div className="bubble">
          Welcome! Type a message to start chatting. Messages will appear with realistic delays.
        </div>
      </div>
      
      {messages.map((message, index) => (
        <Message key={index} {...message} />
      ))}
      
      {skills.length > 0 && <Skills skills={skills} />}
    </div>
  )
}

export default ChatContainer
